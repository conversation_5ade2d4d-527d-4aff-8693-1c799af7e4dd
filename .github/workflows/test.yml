name: Test

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - "**"
  push:
    branches:
      - "main"

jobs:
  test_ui_tars:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install dependencies
        working-directory: codes
        run: |
          python -m pip install --upgrade pip uv
          uv sync
      - name: Run unit tests
        working-directory: codes
        run: |
          make test
