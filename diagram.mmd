graph TB
    subgraph "输入阶段 Input Phase"
        A1[用户任务指令] --> A2[屏幕截图]
        A2 --> A3[历史操作记录]
        A3 --> A4[多模态输入组合]
    end
    
    subgraph "模型推理阶段 Model Inference Phase"
        B1[视觉语言模型 VLM]
        B2[提示模板选择]
        B3[多模态理解]
        B4[推理决策]
        
        B2 --> B21[COMPUTER_USE - 桌面环境]
        B2 --> B22[MOBILE_USE - 移动设备]
        B2 --> B23[GROUNDING - 轻量级]
    end
    
    subgraph "解析处理阶段 Parsing Phase"
        C1[parse_action_to_structure_output]
        C2[文本预处理]
        C3[坐标格式标准化]
        C4[思考内容提取]
        C5[动作指令解析]
        C6[坐标归一化处理]
        
        C1 --> C2
        C2 --> C3
        C3 --> C4
        C4 --> C5
        C5 --> C6
    end
    
    subgraph "代码生成阶段 Code Generation Phase"
        D1[parsing_response_to_pyautogui_code]
        D2[动作类型映射]
        D3[坐标转换]
        D4[参数处理]
        D5[PyAutoGUI代码生成]
        
        D1 --> D2
        D2 --> D3
        D3 --> D4
        D4 --> D5
    end
    
    subgraph "执行阶段 Execution Phase"
        E1[系统操作执行]
        E2[鼠标操作]
        E3[键盘操作]
        E4[滚动操作]
        E5[应用控制]
        
        E1 --> E2
        E1 --> E3
        E1 --> E4
        E1 --> E5
    end
    
    subgraph "反馈阶段 Feedback Phase"
        F1[操作结果获取]
        F2[新屏幕截图]
        F3[状态更新]
        F4[历史记录更新]
    end
    
    %% 主要数据流
    A4 --> B1
    B21 --> B3
    B22 --> B3
    B23 --> B3
    B1 --> B3
    B3 --> B4
    B4 --> C1
    
    C6 --> D1
    D5 --> E1
    E5 --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
    
    %% 反馈循环
    F4 -.-> A3
    F2 -.-> A2
    
    %% 样式定义
    classDef inputPhase fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef modelPhase fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
    classDef parsePhase fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef codePhase fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef execPhase fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef feedbackPhase fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class A1,A2,A3,A4 inputPhase
    class B1,B2,B3,B4,B21,B22,B23 modelPhase
    class C1,C2,C3,C4,C5,C6 parsePhase
    class D1,D2,D3,D4,D5 codePhase
    class E1,E2,E3,E4,E5 execPhase
    class F1,F2,F3,F4 feedbackPhase